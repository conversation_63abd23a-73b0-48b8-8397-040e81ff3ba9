import {useIsFocused, useScrollToTop} from '@react-navigation/native';
import React, {useCallback, useContext, useEffect, useState} from 'react';
import {
  Image,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  View,
  FlatList,
  Modal,
  Pressable,
} from 'react-native';

import {AppStateContext, PageRefreshContext} from '..';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import PostRowPlaceholder from '../components/placeholder/PostRowPlaceholder';
import PostCard from '../components/post/PostCard';
import StoryComponent from '../components/story/StoryComponent';
import Colors from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import {
  DiffTimeRefresh,
  HOME_PAGE_REFRESH_TIME,
  _RedirectionErrorList,
  _UnauthErrorList,
} from '../utils/Appconfig';
import {RedirectionUrlFunction} from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import {ActivityIndicator} from 'react-native-paper';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import CustomStatusBar from '../components/common/CustomStatusBar';
import appData from '../data/Data';
import {
  checkValueLength,
  currentTimeDiffCheck,
  getSecondsBetweenDates,
} from '../utils/Utils';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AuthValidation} from '../utils/AuthValidation';
import HomeIntroPopup from '../components/introslider/HomeIntroPopup';
import {_setShowHomeItroIcon} from '../utils/AuthLogin';
import TopProfileListComponent from '../components/profile/TopProfileListComponent';
import EntutoTextView from '../components/common/EntutoTextView';
import SharePostProfileFeature from '../components/common/SharePostProfileFeature';
import ErrorMessages from '../constants/ErrorMessages';
import useDefaultStyle from '../theme/useDefaultStyle';
import PostCardNew from '../components/post/PostCardNew';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import LOGO_BLACK from '../assets/Images/logo_black.png';
import LOGO_WHITE from '../assets/Images/logo_white.png';
import HomePlaylistCard from '../components/playlist/HomePlaylistCard';
import TempData from '../data/TempData';
import {useSelector} from 'react-redux';
import {useDispatch} from 'react-redux';
import {refreshHomePage} from '../store/slice/homPageRefreshSlice';

const ITEM_HEIGHT = 460;
const HomeScreen = ({route, navigation}) => {
  const {cameFrom} = route.params;

  const [postList, setpostList] = useState([]);
  const {
    showHomeIntro,
    changeShowHomeIntro,
    fullUserDetails,
    changeUserDetails,
    changeUserProfileImage,
    newNotificationCame,
    homepagePostDataBackup,
  } = useContext(AppStateContext);
  const homePageRefresh = useSelector(
    state => state.homPageRefreshSlice.value.refresh,
  );
  const dispatch = useDispatch();
  const {defaultStyle} = useDefaultStyle();
  const [showLoading, setShowLoading] = useState(false);
  const [progressLoading, setprogressLoading] = useState(true);
  const [listRefresh, setlistRefresh] = useState(false);

  const [errorMsg, seterrorMsg] = useState('');
  const [refreshKey, setrefreshKey] = useState(Math.random());
  const isFocused = useIsFocused();
  const RowsPerPage = 10; //Don't change this value this is fixed
  const [startRecord, setstartRecord] = useState(0);
  const [bottomLoading, setbottomLoading] = useState(false);
  const [isNoDataFound, setisNoDataFound] = useState(false);
  const [bottomReachTime, setbottomReachTime] = useState(new Date());
  const [loggedInProfileSeq, setLoggedInProfileSeq] = useState(-1);
  let flatListRef;
  const scrollRefVal = React.useRef(null);
  const [showIntroBox, setshowIntroBox] = useState(false);
  const [firstTime, setFirstTime] = useState(true);
  const theme = useSTheme();
  const style = useSThemedStyles(styles);

  const [newNotiCame, setNewNotiCame] = useState(true);
  useEffect(() => {
    setNewNotiCame(newNotificationCame);
  }, [newNotificationCame]);

  useScrollToTop(scrollRefVal);

  useEffect(() => {
    setprogressLoading(true);
    setShowLoading(true);
    getUserProfileService();
    const timeOutVal = setTimeout(() => {
      saveFCMKey();
      getExploreDataService();
    }, 1500);

    return () => {
      clearTimeout(timeOutVal);
    };
  }, []);
  useEffect(() => {
    // if (showHomeIntro) {
    //     setshowIntroBox(true)
    // }
    // setDisplaySharePopup(true);
  }, []);
  React.useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      if (appData.__HomePageRefreshCheck != appData.__HomePageRefresh) {
        setprogressLoading(true);
        setShowLoading(true);
        getUserProfileService();
        appData.__HomePageRefreshCheck = appData.__HomePageRefresh;
      } else if (appData._homePagePostRefresh == 'YES') {
        appData._homePagePostRefresh = 'NO';
        refreshHomePagePostService();
      }
    });
    return unsubscribe;
  }, [navigation]);

  const refreshHomePagePostService = () => {
    let refreshPostService = false;
    if (
      currentTimeDiffCheck(
        homepagePostDataBackup.homePageDataStoreTime,
        HOME_PAGE_REFRESH_TIME,
      )
    ) {
      homepagePostDataBackup.homePageDataStoreTime = new Date();
      refreshPostService = true;
    }
    // console.log("Refresh Page Else", new Date());
    // console.log("homepagePostDataBackup Length", homepagePostDataBackup.postStatusChangeData);
    // console.log("Execute First", new Date());
    // console.log("refreshPostService", refreshPostService);
    if (!refreshPostService) {
      if (homepagePostDataBackup.postStatusChangeData.length != 0) {
        setlistRefresh(false);
        const tempList = [...homepagePostDataBackup.homePageData];
        tempList.forEach(obj => {
          homepagePostDataBackup.postStatusChangeData.forEach(
            postStatusChangeData => {
              if (postStatusChangeData.post_seq === obj.post_seq) {
                //Like And Reaction
                if (postStatusChangeData.reactionType === 'LIKE') {
                  obj.likes = postStatusChangeData.count;
                  obj.is_liked =
                    postStatusChangeData.action_code === 'SUBMIT_POST_LIKE'
                      ? 'YES'
                      : 'NO';
                } else {
                  const reaction =
                    obj.user_reactions[postStatusChangeData.reactionType];
                  if (reaction) {
                    reaction.count = postStatusChangeData.count;
                    reaction.selected =
                      postStatusChangeData.action_code === 'SUBMIT_POST_LIKE'
                        ? 'YES'
                        : 'NO';
                  } else {
                    obj.user_reactions[postStatusChangeData.reactionType] = {
                      count: postStatusChangeData.count,
                      selected:
                        postStatusChangeData.action_code === 'SUBMIT_POST_LIKE'
                          ? 'YES'
                          : 'NO',
                    };
                  }
                }
                //Comment
                if (
                  postStatusChangeData.action_code === 'SUBMIT_POST_COMMENT'
                ) {
                  obj.comments = postStatusChangeData.count;
                  obj.is_commented = 'YES';
                } else if (
                  postStatusChangeData.action_code === 'REMOVE_POST_COMMENT'
                ) {
                  obj.comments = postStatusChangeData.count;
                  obj.is_commented = 'NO';
                }
                //Bookmark
                if (postStatusChangeData.action_code === 'ADD_BOOKMARK') {
                  obj.is_bookmarked = 'YES';
                } else if (
                  postStatusChangeData.action_code === 'REMOVE_BOOKMARK'
                ) {
                  obj.is_bookmarked = 'NO';
                }
              }
              //Follow
              if (
                postStatusChangeData.type == 'FOLLOW' &&
                postStatusChangeData.profileSeq == obj.profile_seq
              ) {
                if (postStatusChangeData.action_code == 'FOLLOW_PROFILE') {
                  obj.is_following = 'YES';
                } else if (
                  postStatusChangeData.action_code == 'UNFOLLOW_PROFILE'
                ) {
                  obj.is_following = 'NO';
                }
              }
            },
          );
        });
        // console.log("Execute End", new Date());
        setstartRecord(homepagePostDataBackup.homeCurrentStartRecord);
        homepagePostDataBackup.postStatusChangeData = [];
        setpostList([...[], ...tempList]);
      } else {
        refreshPostService = true;
      }
    }
    if (refreshPostService) {
      setlistRefresh(true);
      getHomePostListService(0, RowsPerPage);
    }
  };

  useEffect(() => {
    if (!listRefresh && homePageRefresh == 'YES') {
      flatListScrollToTop();
      setlistRefresh(true);
      getHomePostListService(0, RowsPerPage);
    }
  }, [homePageRefresh]);

  function getUserProfileService() {
    let profileSeq = -1;
    if (fullUserDetails.hasOwnProperty('_profile_seq')) {
      profileSeq = fullUserDetails._profile_seq;
    }
    setLoggedInProfileSeq(profileSeq);
    let hashMap = {
      _action_code: '11:GET_USER_PROFILE',
      req_profile_seq: profileSeq,
    };

    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        let display_name = '-';
        if (data.data[0].display_name !== null) {
          display_name = data.data[0].display_name;
        }
        let hasBankDetails = 'NO';
        if (data.data[0].bank_account !== null) {
          if (data.data[0].bank_account.length !== 0) {
            hasBankDetails = 'YES';
          }
        }
        let hasStateCity = 'NO';
        if (data.data[0].state !== null) {
          if (data.data[0].state.length !== 0) {
            hasStateCity = 'YES';
          }
        }
        let userDeatails = {
          _user_handle: data.data[0].user_handle,
          _user_account_type: data.data[0].type,
          _user_display_name: display_name,
          _has_bank_details: hasBankDetails,
          _is_profile_verified: data.data[0].is_verified,
          _has_state_city: hasStateCity,
          _profile_picture: data.data[0].profile_picture,
          _cover_image: data.data[0].cover_image,
        };
        let uiColor = 'COLOR_1';
        if (data.data[0].hasOwnProperty('ui_colour')) {
          if (checkValueLength(data.data[0].ui_colour)) {
            uiColor = data.data[0].ui_colour;
          }
        }
        theme.changeThemeColor(uiColor);
        changeUserProfileImage(data.data[0].profile_picture);
        setShowLoading(false);
        changeUserDetails(userDeatails);
        getHomePostListService(0, RowsPerPage);
        setFirstTime(false);
        if (
          data.data[0].is_verify_notified == 'NO' &&
          data.data[0].is_verified == 'YES'
        ) {
          updateVerifyNotifyService(profileSeq);
          setTimeout(() => {
            openShareModal();
          }, 500);
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          setShowLoading(false);
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          if (_UnauthErrorList.includes(errorCode)) {
            AuthValidation(errorCode, data, navigation);
          }
          setShowLoading(false);
          setprogressLoading(false);
          setFirstTime(true);
        }
      },
    );
  }
  useEffect(() => {
    homepagePostDataBackup.homePageData = postList;
  }, [postList]);

  function getHomePostListService(startRecordV, rowsPerPage) {
    // setisNoDataFound(false);
    // setprogressLoading(false);
    // setbottomLoading(false);
    // setlistRefresh(false);
    // return;
    setstartRecord(startRecordV);
    let hashMap = {
      _action_code: '11:GET_POSTS',
      _start_row: startRecordV,
      _rows_page: rowsPerPage,
    };

    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        if (parseInt(startRecordV) == 0) {
          const dataArray = [];
          data.data.map((obj, i) => {
            obj.ui_type = 1;
            obj.show_list = [];
            dataArray.push(obj);
            if (i == 1) {
              if (data.show_list && data.show_list.length != 0) {
                obj.ui_type = 2;
                obj.show_list = data.show_list;
                dataArray.push(obj);
              }
            }
          });
          setpostList([...[], ...data.data]);
        } else {
          data.data.map((obj, i) => {
            obj.ui_type = 1;
            obj.show_list = [];
            if (i == 8) {
              if (data.show_list && data.show_list.length != 0) {
                obj.ui_type = 2;
                obj.show_list = data.show_list;
              }
            }
          });
          setpostList(prevPostsData => [...prevPostsData, ...data.data]);
          // setpostList([...postList, ...data.data]);
        }
        homepagePostDataBackup.homeCurrentStartRecord = startRecordV;

        setisNoDataFound(false);
        setprogressLoading(false);
        setbottomLoading(false);
        setlistRefresh(false);
        if (homePageRefresh == 'YES') {
          dispatch(refreshHomePage('NO'));
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (homePageRefresh == 'YES') {
          dispatch(refreshHomePage('NO'));
        }
        if (_RedirectionErrorList.includes(errorCode)) {
          setlistRefresh(false);
          setprogressLoading(false);
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          if (parseInt(startRecordV) == 0) {
            setpostList([]);
          }
          setlistRefresh(false);
          setbottomLoading(false);
          setprogressLoading(false);
          setisNoDataFound(true);
        }
      },
    );
  }
  const postCardClick = (clickID, obj) => {};
  const homPlaylistClick = (clickID, obj) => {
    if (clickID == 'CLICK') {
      // console.log("obj", obj);
      // navigation.navigate('OthersProfileScreen', {
      //     profileSeq: obj.profile_seq,
      //     tabType: "PLAYLIST"
      // });
      navigation.navigate('PlaylistSeasonScreen', {
        showSeq: obj.show_seq,
      });
      // navigation.navigate("PlaylistShowScreen", { reqUserSeq: obj.userSeq })
    }
  };
  const renderItem = useCallback(
    ({item, index}) => {
      if (item.ui_type == 1) {
        return (
          <PostCard
            itemData={item}
            navigation={navigation}
            isMyProfile={false}
            postCardClick={postCardClick}
            index={index}
          />
        );
      } else if (item.ui_type == 2) {
        return (
          <HomePlaylistCard
            itemData={item}
            navigation={navigation}
            isMyProfile={false}
            homPlaylistClick={homPlaylistClick}
          />
        );
      }
    },
    [postList],
  );
  const handleRefresh = () => {
    setlistRefresh(true);
    setprogressLoading(true);
    getHomePostListService(0, RowsPerPage);
  };
  const logoBtnPress = () => {
    flatListScrollToTop();
  };
  const flatListScrollToTop = () => {
    flatListRef.scrollToOffset({animated: true, offset: 0});
  };
  const handleEndRefresh = () => {
    if (!bottomLoading) {
      if (!isNoDataFound) {
        let startRec = startRecord + RowsPerPage;
        setbottomLoading(true);
        getHomePostListService(startRec, RowsPerPage);
        setbottomReachTime(new Date());
      }
    }
  };
  const saveFCMKey = async () => {
    // let isSaveFCMKey = await AsyncStorage.getItem('_isSaveFCMKey');
    let isSaveFCMKey = false;
    if (!isSaveFCMKey) {
      let fcmToken = await AsyncStorage.getItem('_FCM_TOKEN_A');
      if (!fcmToken) {
        try {
          fcmToken = await messaging().getToken();
          // console.log(fcmToken, "the New token");
          AsyncStorage.setItem('_FCM_TOKEN_A', fcmToken);
        } catch (error) {
          // console.log(error, "error raised in fcm token")
        }
      }
      if (fcmToken) {
        saveFCMKeyService(fcmToken);
      }
    }
  };
  const saveFCMKeyService = fcmToken => {
    let hashMap = {
      _action_code: '11:SAVE_FCM_KEY',
      fcm_key: fcmToken,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        try {
          AsyncStorage.setItem('_isSaveFCMKey', 'YES');
        } catch (error) {}
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
        }
      },
    );
  };
  const homeIntroPopupPress = () => {
    _setShowHomeItroIcon('NO');
    setshowIntroBox(false);
    changeShowHomeIntro(false);
  };
  const listFooterComponent = useCallback(() => {
    return (
      <View
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          paddingVertical: 15,
        }}>
        <ActivityIndicator
          animating={true}
          color={theme.colors.primaryColor}
          size={'large'}
        />
      </View>
    );
  }, []);

  const listHeaderComponent = useCallback(() => {
    return (
      <View style={style.storyContainer}>
        {/* <TopProfileListComponent navigation={navigation} refreshPage={listRefresh} /> */}
        {/* <StoryComponent navigation={navigation} refreshPage={listRefresh} /> */}
        {/* <TopMenuBox /> */}
      </View>
    );
  }, []);
  const keyExtractor = (item, index) => `${item.post_seq}_${index}`;

  const searchBtnClick = () => {
    navigation.navigate('SearchResultScreen');
  };
  const notificationBtnClick = () => {
    navigation.navigate('NotificationScreen');
  };
  const cameraBtnPress = () => {};
  const updateVerifyNotifyService = profileSeq => {
    let hashMap = {
      _action_code: '11:UPDATE_VERIFY_NOTIFY',
      profile_seq: profileSeq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
      },
      (errorCode, errorMessage, data) => {
        // failure method
      },
    );
  };
  const [displaySharePopup, setDisplaySharePopup] = useState(false);
  const openShareModal = () => {
    setDisplaySharePopup(true);
  };
  const sharePostProfileCallback = (clickID, obj) => {
    if (clickID == 'CLOSE') {
      setDisplaySharePopup(false);
    }
  };
  function getExploreDataService() {
    let hashMap = {
      _action_code: '11:DO_TOPIC_SEARCH',
      _start_row: 0,
      _rows_page: 21,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        data.data.map(item => (item.disableClick = false));
        TempData.topicsBackupData = data.data;
      },
      (errorCode, errorMessage, data) => {
        // failure method
      },
    );
  }
  return (
    <>
      {/* <CustomStatusBar translucent={false} hidden={false} />   */}
      <CustomProgressDialog showLoading={showLoading} />
      <View style={style.homeTopContainer}>
        <View>
          <TouchableOpacity
            style={{paddingHorizontal: 8}}
            onPress={() => logoBtnPress()}>
            <Image
              style={style.homeTopImage}
              source={theme.appThemeType == 'DARK' ? LOGO_WHITE : LOGO_BLACK}
            />
          </TouchableOpacity>
        </View>
        <View style={{marginLeft: 'auto'}}>
          <Pressable
            onPress={() => notificationBtnClick()}
            android_ripple={{
              color: theme.colors.pressableRippleColor,
              borderless: true,
              radius: 30,
            }}
            style={{paddingVertical: 8}}>
            <View style={{paddingHorizontal: 8}}>
              <Image
                style={style.homeNotificationIcon}
                source={require('../assets/Images/icon/notification.png')}
                resizeMode="contain"
              />
              {newNotiCame ? (
                <View
                  style={{
                    position: 'absolute',
                    right: 8,
                    top: 0,
                    backgroundColor: 'red',
                    borderRadius: 8,
                    width: 8,
                    height: 8,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}></View>
              ) : null}
            </View>
          </Pressable>
        </View>
      </View>

      {errorMsg.length != 0 ? (
        <View style={defaultStyle.errorBoxOutside}>
          <SuccessFailureMsgBox
            visibleAllTime={true}
            alertMsg={errorMsg}
            alertKey={refreshKey}
          />
        </View>
      ) : null}
      <View
        scrollRef={scrollRefVal}
        style={{
          flex: 1,
          backgroundColor: theme.colors.backgroundColor,
          // marginTop: 100,
        }}>
        {progressLoading ? (
          <PostRowPlaceholder />
        ) : (
          <>
            {/* <View style={{ minHeight: Dimensions.screenHeight - 50, width: Dimensions.screenWidth }}> */}
            <FlatList
              keyboardShouldPersistTaps={'handled'}
              contentContainerStyle={{paddingBottom: 64}}
              ref={ref => {
                flatListRef = ref;
              }}
              data={postList}
              removeClippedSubviews={true}
              ListHeaderComponent={listHeaderComponent}
              ListFooterComponent={bottomLoading && listFooterComponent}
              maxToRenderPerBatch={1000}
              windowSize={60}
              updateCellsBatchingPeriod={50}
              initialNumToRender={50}
              // estimatedItemSize={200}
              renderItem={renderItem}
              disableVirtualization
              keyExtractor={(item, index) => `${item.post_seq}_${index}`}
              onEndReached={handleEndRefresh}
              onEndReachedThreshold={15} //Change from 20 to 10
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
              getItemLayout={(data, index) => {
                return {
                  index,
                  length: ITEM_HEIGHT,
                  offset: ITEM_HEIGHT * index,
                };
              }}
              refreshControl={
                <RefreshControl
                  tintColor={theme.colors.primaryColor}
                  refreshing={progressLoading}
                  onRefresh={() => handleRefresh()}
                />
              }
              // ListEmptyComponent={<View style={style.playBtnBox}>
              //     <ActivityIndicator size={32} />
              // </View>}
            />
            {/* In Upper Flatlist getItemLayout should be commented on Android */}
            {/* </View> */}
          </>
        )}
      </View>
      {showIntroBox ? (
        <HomeIntroPopup
          navigation={navigation}
          homeIntroPopup={homeIntroPopupPress}
        />
      ) : null}
      {/* They Want to remove this at 09-09-24  */}
      {/* <Modal
                animationType="slide"
                transparent
                visible={displaySharePopup}
                onRequestClose={() => setDisplaySharePopup(false)}
            >
                <SharePostProfileFeature shareBody1={ErrorMessages.verifyProfileBody1}
                    shareBody2={ErrorMessages.verifyProfileBody2}
                    shareType="PROFILE"
                    shareSeq={loggedInProfileSeq}
                    sharePostProfileCallback={sharePostProfileCallback} />
            </Modal> */}
    </>
  );
};

export default HomeScreen;
const TopMenuBox = () => {
  const [selectedMenuItem, setSelectedMenuItem] = useState('RECENT_POST');
  const topMenuTabPress = menuItem => {
    setSelectedMenuItem(menuItem);
  };
  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginStart: 8,
        marginEnd: 10,
        marginTop: 10,
      }}>
      <HomeTabMenuItem
        label="Recent Post"
        tabValue="RECENT_POST"
        selectedMenu={selectedMenuItem}
        onTabChange={topMenuTabPress}
      />
      <HomeTabMenuItem
        label="Most Popular"
        tabValue="MOST_POPULAR"
        selectedMenu={selectedMenuItem}
        onTabChange={topMenuTabPress}
      />
      <HomeTabMenuItem
        label="Trending"
        tabValue="TRENDING"
        selectedMenu={selectedMenuItem}
        onTabChange={topMenuTabPress}
      />
    </View>
  );
};

const HomeTabMenuItem = ({
  label = '',
  tabValue = '',
  selectedMenu = '',
  onTabChange = null,
}) => {
  return (
    <TouchableOpacity onPress={() => onTabChange(tabValue)}>
      <View style={{justifyContent: 'center', alignItems: 'center'}}>
        <EntutoTextView
          style={{
            color: selectedMenu == tabValue ? '#E59D80' : '#77838F',
            fontSize: 13,
          }}>
          {label}
        </EntutoTextView>
        <View
          style={{
            width: 8,
            height: 8,
            borderRadius: 8,
            backgroundColor:
              selectedMenu == tabValue ? '#E59D80' : 'transparent',
            marginTop: 3,
          }}
        />
      </View>
    </TouchableOpacity>
  );
};
const styles = theme =>
  StyleSheet.create({
    homeTopContainer: {
      backgroundColor: theme.colors.backgroundColor,
      flexDirection: 'row',
      alignItems: 'center',
      zIndex: 999,
      height: 64,
      paddingHorizontal: theme.dimensions.defaultSideGap,
      // borderBottomWidth: 1,
      // borderBottomColor: '#ddd'
    },
    storyContainer: {
      paddingHorizontal: theme.dimensions.defaultSideGap,
      // marginTop: 8,
    },
    homeTopImage: {
      width: 84,
      height: 28,
      resizeMode: 'contain',
    },
    homeNotificationIcon: {
      width: 28,
      height: 28,
      tintColor: theme.colors.primaryColor,
    },
    playBtnBox: {
      height: 63,
      width: 63,
      position: 'absolute',
      top: '43%',
      left: '43%',
      backgroundColor: '#00000080',
      borderRadius: 15,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.5,
      shadowRadius: 1.41,

      // elevation: 2,
    },
  });
