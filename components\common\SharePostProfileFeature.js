import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import Share from 'react-native-share';

import TwitterIcon from '../../assets/Images/social_icon/twitter.png';
import SnapchatIcon from '../../assets/Images/social_icon/snapchat.png';
import WhatsappIcon from '../../assets/Images/social_icon/whatsapp.png';
import FacebookIcon from '../../assets/Images/social_icon/facebook.png';
import LinkedinIcon from '../../assets/Images/social_icon/linkedin.png';
import EntutoTextView from './EntutoTextView';
import { creationOfCopyLink } from '../../utils/Utils';
import LottieView from 'lottie-react-native';
import ServerConnector from '../../utils/ServerConnector';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import CLOSED_ICON from '../../assets/Images/icon/close_icon.png';
import CustomStatusBar from './CustomStatusBar';

const SharePostProfileFeature = ({
    shareType = "POST", shareSeq = -1, shareBody1 = "", shareBody2 = "",
    sharePostProfileCallback = null
}) => {
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const shareBtnPress = (shareTypeV) => {
        onShare(shareTypeV);
    }
    const onShare = async (shareTypeV) => {
        let copyLinkText = creationOfCopyLink(shareType, shareSeq);
        let social = Share.Social.TWITTER;
        if (shareTypeV == "SNAPCHAT") {
            social = Share.Social.SNAPCHAT;
        }
        else if (shareTypeV == "WHATSAPP") {
            social = Share.Social.WHATSAPP;
        }
        else if (shareTypeV == "FACEBOOK") {
            social = Share.Social.FACEBOOK;
        }
        else if (shareTypeV == "LINKEDIN") {
            social = Share.Social.LINKEDIN;
        }
        if (shareType == "POST") {
            submitPostShareCountService();
        }
        const shareOptions = {
            message: "Exclusive content on SoTrue\n",
            url: copyLinkText,
            social: social,
            backgroundBottomColor: '#fefefe',
            backgroundTopColor: '#906df4',
        }
        try {
            await Share.shareSingle(shareOptions);
        } catch (error) {
            // console.log(error);
            try {
                await Share.open(shareOptions);
            }
            catch (error) {

            }
            // Alert.alert("Error", "No Activity found to handle Intent");
        }
    };
    const onBackdropPress = () => {
        if (sharePostProfileCallback != null) {
            sharePostProfileCallback("CLOSE", {})
        }
    }

    function submitPostShareCountService() {
        let hashMap = {
            _action_code: "11:UPDATE_SHARE_COUNT",
            post_seq: shareSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
        }, (errorCode, errorMessage, data) => { // failure method

        });
    }
    return (
        <>
            <CustomStatusBar
                translucent={false} hidden={false} />
            <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', backgroundColor: theme.colors.backgroundColor }}>
                <View style={style.closeBox}>
                    <TouchableOpacity onPress={() => onBackdropPress()}>
                        <Image source={CLOSED_ICON} style={style.closeBoxIcon} />
                    </TouchableOpacity>
                </View>

                <LottieView source={require('../../assets/jsonfile/Celebration.json')} autoPlay loop
                    resizeMode='cover' style={style.lottie} />
                <View style={{ zIndex: 2 }}>
                    <View style={style.awesomeTextBox}>
                        <EntutoTextView style={style.awesomeText}>Awesome!</EntutoTextView>
                    </View>
                    <View style={style.body1Box}>
                        <Text style={style.body1BoxText} allowFontScaling={false}>{shareBody1}</Text>
                    </View>
                    <View style={style.body2Box}>
                        <Text style={style.body2BoxText} allowFontScaling={false}>{shareBody2}</Text>
                    </View>
                    <View>
                        <EntutoTextView style={style.shareItNowText}>Share it now</EntutoTextView>
                    </View>
                    <View style={style.shareSocialBox}>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("TWITTER")}>
                                <Image source={TwitterIcon} resizeMode={'contain'} style={style.shareSocialIcon} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("SNAPCHAT")}>
                                <Image source={SnapchatIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("WHATSAPP")}>
                                <Image source={WhatsappIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("FACEBOOK")}>
                                <Image source={FacebookIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("LINKEDIN")}>
                                <Image source={LinkedinIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
                {/* <TouchableOpacity style={style.shareModal} onPress={() => onBackdropPress()}>
                <TouchableOpacity style={style.modalContainer} activeOpacity={1}>

                    <View style={style.awesomeTextBox}>
                        <EntutoTextView style={style.awesomeText}>Awesome!</EntutoTextView>
                    </View>
                    <View style={style.body1Box}>
                        <Text style={style.body1BoxText}>{shareBody1}</Text>
                    </View>
                    <View style={style.body2Box}>
                        <Text style={style.body2BoxText}>{shareBody2}</Text>
                    </View>
                    <View>
                        <EntutoTextView style={style.shareItNowText}>Share it now</EntutoTextView>
                    </View>
                    <View style={style.shareSocialBox}>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("TWITTER")}>
                                <Image source={TwitterIcon} resizeMode={'contain'} style={style.shareSocialIcon} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("SNAPCHAT")}>
                                <Image source={SnapchatIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("WHATSAPP")}>
                                <Image source={WhatsappIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("FACEBOOK")}>
                                <Image source={FacebookIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("LINKEDIN")}>
                                <Image source={LinkedinIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                    </View>
                </TouchableOpacity>

            </TouchableOpacity> */}

            </View>
        </>
    )
}

export default SharePostProfileFeature

const styles = theme => StyleSheet.create({
    shareModal: {
        flex: 1,
        backgroundColor: theme.colors.modalBackground,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    modalContainer: {
        width: '80%',
        backgroundColor: theme.colors.modalContainerBackground,
        paddingHorizontal: 10,
        paddingVertical: 10,
        borderRadius: 20,
        elevation: 20,
    },
    awesomeTextBox: {
        marginBottom: 15,
        marginTop: 45
    },
    awesomeText: {
        fontSize: theme.calculateFontSize(theme.dimensions.shareProfileAwesomeText),
        fontFamily: theme.getFontFamily('bold'),
        color: theme.colors.awesomeText,
        textAlign: 'center',
        textShadowColor: 'rgba(0, 0, 0, 0.1)',
        textShadowOffset: { width: 1, height: 4 },
        textShadowRadius: 10
    },
    body1Box: {
        marginTop: 12,
        marginHorizontal: 15
    },
    body1BoxText: {
        fontSize: theme.calculateFontSize(theme.dimensions.shareProfileBody1BoxText),
        color: theme.colors.primaryTextColor,
        textAlign: 'center',
    },
    body2Box: {
        marginTop: 12,
        marginBottom: 20,
        marginHorizontal: 15
    },
    body2BoxText: {
        fontSize: theme.calculateFontSize(theme.dimensions.shareProfileBody2BoxText),
        color: theme.colors.awesomeText,
        alignSelf: 'center',
        textAlign: 'center'
    },
    shareItNowText: {
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.shareProfileShareItNowText),
        alignSelf: 'center',
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
        marginTop: 45
    },
    shareSocialBox: {
        marginTop: 25,
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        marginBottom: 50,
    },
    shareSocialIcon: {
        width: 28,
        height: 28,
        tintColor: theme.colors.shareSocialIconTintColor
    },
    shareSocialTintIcon: {
        tintColor: theme.colors.shareSocialTintIconTintColor
    },
    lottie: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
    },
    closeBox: {
        position: 'absolute',
        top: 16,
        right: 16,
        zIndex: 99,
    },
    closeBoxIcon: {
        width: 24,
        height: 24,
        tintColor: theme.colors.cancelBtnText,
    }
})