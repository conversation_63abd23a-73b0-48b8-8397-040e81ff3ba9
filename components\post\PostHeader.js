import React from 'react';
import {
  Image,
  StyleSheet,
  View,
  TouchableOpacity,
  ActivityIndicator,
  Text,
} from 'react-native';
import EntutoTextView from '../common/EntutoTextView';
import ProgressiveImage from '../common/ProgressiveImage';
import ProfileImagePlaceholder from '../../assets/Images/full_user_image_place_holder.png';
import VerifiedIcon from '../../assets/Images/icon/verifiedicon.png';
import ThreeDotVerticalIcon from '../../assets/Images/icon/profile_three_dot.png';
import BookmarkBtnComponent from '../common/BookmarkBtnComponent';
import BOOKMARK_ICON from '../../assets/Images/icon/bookmark.png';
import {hasImageUrlExist} from '../../utils/Utils';
// import {UserHandlePrefix} from '../../utils/Appconfig';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';

const PostHeader = ({
  itemData,
  headerType = 'HOMESCREEN', // HOMESCREEN, REELS, FULLSCREEN
  goToProfile,
  followBtnPress,
  followServiceLoading,
  isFollowing,
  threeDotMenuClick,
  bookmarkIconBtnClick,
  bookmarkBtnDisable,
  postIsBookmark,
  showBookmarkIcon,
  blockPost,
  style,
}) => {
  const theme = useSTheme();
  const headerStyles = useSThemedStyles(styles);
  console.log('itemData', itemData);
  return (
    <>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          flex: 1,
        }}>
        <ProgressiveImage
          style={style.NSProfileViewImage}
          source={
            hasImageUrlExist(itemData.profile_picture)
              ? {uri: itemData.profile_picture}
              : null
          }
          defaultImageSource={ProfileImagePlaceholder}
          resizeMode="cover"
        />
        <View style={{marginLeft: 10, flex: 1}}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 2,
            }}>
            <TouchableOpacity
              onPress={() => goToProfile(itemData.profile_seq)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <EntutoTextView style={style.headerDisplayName}>
                {itemData.display_name}
              </EntutoTextView>
              {itemData.is_verified == 'YES' ? (
                <Image
                  style={style.verifiedIcon}
                  source={VerifiedIcon}
                  resizeMode={'contain'}
                />
              ) : null}
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => followBtnPress()}
              disabled={followServiceLoading}>
              <View style={style.bellowFollowBtn}>
                {followServiceLoading ? (
                  <ActivityIndicator size={12} />
                ) : (
                  <EntutoTextView style={style.bellowFollowBtnText}>
                    {isFollowing ? 'FOLLOWING' : 'FOLLOW'}
                  </EntutoTextView>
                )}
              </View>
            </TouchableOpacity>
          </View>
          <EntutoTextView style={style.taggedText}>
            <Text style={{color: theme.colors.primaryTextColor}}>Cast:</Text>
            Siddhantkakar, prithvirajprasad ...
          </EntutoTextView>
        </View>
      </View>
    </>
  );
};

const styles = () =>
  StyleSheet.create({
    // Add any additional styles needed for the header component
  });

export default PostHeader;
