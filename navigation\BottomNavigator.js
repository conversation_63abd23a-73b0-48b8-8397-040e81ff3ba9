import React, { memo, useContext, useEffect, useState } from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import HomeScreen from '../screens/HomeScreen';
import NotificationScreen from '../screens/NotificationScreen';
import SearchScreen from '../screens/SearchScreen';
import { Image, ImageBackground, Platform, Pressable, Text, TouchableOpacity, View } from 'react-native';
import AddPostScreen from '../screens/AddPostScreen';
import { AppStateContext, PageRefreshContext } from '..';
import Colors from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import MyProfileScreen from '../screens/MyProfileScreen';
import SettingScreen from '../screens/SettingScreen';
import ProfileImageComponent from '../components/common/ProfileImageComponent';
const HomeTabNav = createBottomTabNavigator();
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import { TouchableWithoutFeedback } from 'react-native-gesture-handler';
import EntutoTextView from '../components/common/EntutoTextView';
import VideoContentScreen from '../screens/VideoContentScreen';
import appData from '../data/Data';
import SearchResultScreen from '../screens/SearchResultScreen';
import useSTheme from '../theme/useSTheme';
import { useDispatch, useSelector } from 'react-redux';
import { refreshHomePage } from '../store/slice/homPageRefreshSlice';

let timer = null;
const TIMEOUT = 300
const debounce = (onSingle, onDouble) => {
    if (timer) {
        clearTimeout(timer);
        timer = null;
        onDouble();
    } else {
        clearTimeout(timer);
        timer = setTimeout(() => {
            timer = null;
            onSingle();
        }, TIMEOUT);
    }
};

export const BottomNavigator = memo(({ navigation, route }) => {
    const { fullUserDetails, newNotificationCame, userProfileImage } = useContext(AppStateContext);
    const { changeNotificationRefresh, } = useContext(PageRefreshContext);
    const [profileImage, setprofileImage] = useState(null);
    const selectedScreen = route.params != undefined ? route.params.screen : "HomeFeed";
    const [newNotiCame, setnewNotiCame] = useState(false);
    const homePageRefresh = useSelector((state) => state.homPageRefreshSlice.value.refresh);
    const dispatch = useDispatch();
    const theme = useSTheme();
    useEffect(() => {
        setnewNotiCame(newNotificationCame);
    }, [newNotificationCame])


    // useEffect(() => {
    //     setprofileImage(userProfileImage);
    // }, [userProfileImage])

    const CustomTabIcon = ({ isProfile = false, showBadge = false, ...props }) => {

        var icon = props.focused
            ? props.activeSource
            : props.inactiveSource;
        return <View style={{
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: theme.colors.bottomTabBackground,
            width: 47,
            height: 44,
            borderRadius: 7,
            elevation: 2
        }}>
            <Image
                source={icon}
                resizeMode="contain"
                style={{
                    width: 28,
                    height: 28,
                    tintColor: props.focused ? theme.colors.homeBottomTabActiveColor : theme.colors.homeBottomTabInActiveColor

                }}
            />
            {
                showBadge ?
                    <View style={{
                        position: 'absolute',
                        right: 0, top: 0,
                        backgroundColor: 'red',
                        borderRadius: 8,
                        width: 8,
                        height: 8,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                        {/* <Text style={{ color: 'white' }}>1</Text> */}
                    </View>
                    : null
            }

        </View>
    }
    const CustomProfileIcon = ({ ...props }) => {
        return <View style={[{
            height: 32,
            width: 32,
            borderRadius: 24,
            alignItems: 'center',
            justifyContent: 'center'
        }, props.focused && {
            borderWidth: 2,
            borderColor: Colors.primaryColor,
        }
        ]}>
            <ProfileImageComponent
                source={props.icon}
                resizeMode="cover"
                style={{
                    width: 24,
                    height: 24,
                    margin: 5,
                    borderRadius: 24,
                }}

            />
        </View>

    }
    const CustomAddTabIcon = (props) => {

        const [showAddPopup, setshowAddPopup] = useState(false);
        const addBtnPress = () => {
            // navigation.navigate("AddPostScreen");
            navigation.navigate("PostingJourneyScreen");
            // setshowAddPopup(!showAddPopup);//Comment out For Testing
        }
        const postBtnPress = () => {
            setshowAddPopup(false);
            navigation.navigate("AddPostScreen");
        }
        const storyBtnPress = () => {
            setshowAddPopup(false);
            navigation.navigate("AddStoryScreen");
        }
        return <View>
            <TouchableWithoutFeedback onPress={(evt) => setshowAddPopup(false)}>
                <View style={{ width: 0, height: 0, position: 'absolute', left: 0, top: 0 }}></View>
            </TouchableWithoutFeedback>
            <Pressable onPress={() => addBtnPress()}>
                <View style={{
                    // // width: 66,
                    // // height: 66,
                    // // top: -12,
                    // justifyContent: "center",
                    // alignItems: "center",
                    // backgroundColor: "#FFFFFF",
                    // // padding: 10,
                    // borderWidth: 1,
                    // borderColor: "#FFFFFF",
                    // // borderRadius: 66,

                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: theme.colors.bottomTabBackground,
                    width: 47,
                    height: 44,
                    borderRadius: 7,
                    elevation: 2
                }}>

                    <Image source={require('../assets/Images/icon/plus.png')}
                        resizeMode="cover"
                        style={{
                            width: 24,
                            height: 24,
                            tintColor: theme.colors.homeBottomTabInActiveColor,
                            justifyContent: 'center',
                            alignItems: 'center',
                            // transform: [{ rotate: `${showAddPopup ? 45 : 90}deg` }]
                        }} />
                    {/* </ImageBackground> */}
                    <View style={{
                        position: 'absolute',
                        // bottom: 85,
                        bottom: 56,
                        width: 150, minHeight: 100,
                        borderRadius: 20,
                        padding: 8,
                        backgroundColor: '#FFFFFF',
                        shadowColor: "#f3997b",
                        shadowOffset: {
                            width: 1,
                            height: 6,
                        },
                        shadowOpacity: 0.70,
                        shadowRadius: 1.41,
                        elevation: 2,
                        display: `${showAddPopup ? 'flex' : 'none'}`


                    }}>
                        <TouchableOpacity
                            onPress={() => postBtnPress()}>
                            <View style={{ paddingHorizontal: 8, paddingVertical: 8, flexDirection: 'row', alignItems: 'center' }}>
                                <EntutoTextView style={{ color: '#000', fontSize: 16, fontWeight: '600' }}>Post</EntutoTextView>
                                <View style={{ marginLeft: 'auto' }}>
                                    <Image source={require('../assets/Images/icon/Post.png')}
                                        resizeMode="contain"
                                        style={{
                                            width: 22,
                                            height: 22,
                                        }} />
                                </View>

                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => storyBtnPress()}>
                            <View style={{ paddingHorizontal: 8, paddingVertical: 8, flexDirection: 'row', alignItems: 'center' }}>
                                <EntutoTextView style={{ color: '#000', fontSize: 16, fontWeight: '600' }}>Story</EntutoTextView>
                                <View style={{ marginLeft: 'auto' }}>
                                    <Image source={require('../assets/Images/icon/StoryIcon.png')}
                                        resizeMode="contain"
                                        style={{
                                            width: 22,
                                            height: 22,
                                        }} />
                                </View>
                            </View>
                        </TouchableOpacity>
                        <View style={{
                            position: 'absolute',
                            bottom: -38, left: 0, right: 0,
                            alignItems: 'center',
                        }}>
                            <FontAwesome5 name='caret-down' size={65} color={"#FFFFFF"}></FontAwesome5>
                        </View>

                    </View>
                </View>
            </Pressable>
        </View>
    }

    const onSingleTap = () => {
        // console.log('single tap');
    }
    const onDoubleTap = () => {
        if (selectedScreen == "HomeFeed") {
            if (homePageRefresh == "NO") {
                dispatch(refreshHomePage("YES"))
            }
        }
    }
    const homeButtonPress = () => {
        debounce(onSingleTap, onDoubleTap);
    }
    return (
        <HomeTabNav.Navigator
            initialRouteName={selectedScreen}
            detachInactiveScreens={true}
            screenOptions={{
                tabBarShowLabel: false,
                headerShown: false,
                tabBarHideOnKeyboard: true,
                tabBarItemStyle: {
                    height: Dimensions.bottonTabNavHeight,
                },
                tabBarStyle: {
                    minHeight: Dimensions.bottonTabNavHeight,
                    height: Dimensions.bottonTabNavHeight,
                    // maxHeight: Dimensions.bottonTabNavHeight,
                    backgroundColor: "transparent",
                    // shadowColor: "#000",
                    // shadowOffset: {
                    //     width: 0,
                    //     height: 1,
                    // },
                    // shadowOpacity: 0.22,
                    // shadowRadius: 2.22,

                    elevation: 0,
                    borderTopWidth: 0,
                    borderWidth: 0,
                    // borderColor: '#E0E0E0',
                    position: 'absolute',
                    bottom: 16,
                    left: 16,
                    right: 16,
                    borderRadius: 10,
                },
            }}>
            <HomeTabNav.Screen
                name="HomeFeed"
                component={HomeScreen}
                options={{
                    tabBarLabel: 'Home',
                    tabBarIcon: ({ focused }) => (
                        <CustomTabIcon
                            inactiveSource={require('../assets/Images/icon/home.png')}
                            focused={focused}
                            activeSource={require('../assets/Images/icon/home.png')} />
                    ),
                }}
                listeners={({ navigation }) => ({
                    tabPress: (e) => {
                        // e.preventDefault();
                        homeButtonPress();

                        // appData._homePagePostRefresh = "YES"
                    },
                })}
            />
            {/* <HomeTabNav.Screen
                name="MyProfileScreen"
                component={MyProfileScreen}
                tabBarVisible={false}
                options={{
                    tabBarLabel: 'Profile',
                    tabBarButton: () => null,
                    tabBarVisible: false,
                }}
            /> */}
            {/* <HomeTabNav.Screen
                name="SettingScreen"
                component={SettingScreen}
                tabBarVisible={false}
                options={{
                    tabBarLabel: 'Settings',
                    tabBarButton: () => null,
                    tabBarVisible: false,
                }}
            /> */}

            <HomeTabNav.Screen
                name="SearchResultScreen"
                component={SearchResultScreen}
                options={{
                    tabBarLabel: 'Explore Page',
                    tabBarIcon: ({ focused }) => (<CustomTabIcon
                        showBadge={false}
                        inactiveSource={require('../assets/Images/icon/search_icon.png')}
                        focused={focused}
                        activeSource={require('../assets/Images/icon/search_icon.png')}
                    />

                    ),
                }}
                listeners={({ navigation }) => ({
                    tabPress: (e) => {
                        // changeNotificationRefresh(Math.random())
                        e.preventDefault();
                        navigation.navigate("SearchResultScreen", {});
                    },
                })}

            />
            <HomeTabNav.Screen
                name="AddPostFeed"
                component={AddPostScreen}
                options={{
                    tabBarLabel: 'Add',
                    tabBarIcon: ({ focused }) => (
                        <CustomAddTabIcon />
                    ),
                }}
                listeners={({ navigation }) => ({
                    tabPress: (e) => {
                        // Prevent default action
                        e.preventDefault();
                        // Do something with the `navigation` object
                        // navigation.navigate("AddPostScreen"); // Here!!!!!!!!!!!!!!!!!!!!!!!!!!!!
                    },
                })}


            />
            {/* <HomeTabNav.Screen
                name="AddPostFeed"
                component={AddPostScreen}
                options={{
                    tabBarLabel: 'Add',
                    tabBarIcon: ({ focused }) => (
                        <CustomAddTabIcon />
                    ),
                }}
                listeners={({ navigation }) => ({
                    tabPress: (e) => {
                        // Prevent default action
                        e.preventDefault();
                        // Do something with the `navigation` object
                        // navigation.navigate("AddPostScreen"); // Here!!!!!!!!!!!!!!!!!!!!!!!!!!!!
                    },
                })}


            /> */}

            <HomeTabNav.Screen
                name="VideoContentFeed"
                component={VideoContentScreen}
                options={{
                    tabBarLabel: 'Video Content',
                    tabBarIcon: ({ focused }) => (
                        <CustomTabIcon
                            inactiveSource={require('../assets/Images/icon/reel.png')}
                            focused={focused}
                            activeSource={require('../assets/Images/icon/reel.png')} />
                    ),
                }}
                listeners={({ navigation }) => ({
                    tabPress: (e) => {
                        // Prevent default action
                        e.preventDefault();
                        navigation.navigate("VideoContentScreen", {
                            postSeq: -1, postProfileSeq: -1, cameFrom: "BOTTOM_NAV"
                        });
                    },
                })}

            />
            <HomeTabNav.Screen
                name="ProfileFeed"
                component={MyProfileScreen}
                options={{
                    tabBarLabel: 'My Profile',
                    tabBarIcon: ({ focused }) => (
                        <CustomTabIcon
                            inactiveSource={require('../assets/Images/icon/profile_icon.png')}
                            focused={focused}
                            activeSource={require('../assets/Images/icon/profile_icon.png')} />
                    ),


                }}

            />
            {/* <HomeTabNav.Screen
                name="ProfileFeed"
                component={MyProfileScreen}
                options={{
                    tabBarLabel: 'Profile',
                    tabBarIcon: ({ focused }) => (
                        <CustomProfileIcon
                            focused={focused}
                            icon={profileImage} />
                    ),
                }}
            // listeners={({ navigation }) => ({
            //     tabPress: (e) => {
            //         // Prevent default action
            //         e.preventDefault();
            //         navigation.toggleDrawer();
            //         // Do something with the `navigation` object
            //         // navigation.navigate("SignupScreen"); // Here!!!!!!!!!!!!!!!!!!!!!!!!!!!!
            //     },
            // })}

            /> */}
        </HomeTabNav.Navigator>
    );
});