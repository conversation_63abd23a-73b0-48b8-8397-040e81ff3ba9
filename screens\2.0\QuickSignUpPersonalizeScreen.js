import {
  Image,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Alert,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useDefaultStyle from '../../theme/useDefaultStyle';
import {_setAppThemeType} from '../../utils/AuthLogin';
import CustomStatusBar from '../../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../../components/common/LoginSignUpLinearGrad';
import {ScrollView} from 'react-native';
import ModuleAppBar from '../../components/loginModule/ModuleAppBar';
import CustomProgressDialog from '../../components/common/CustomProgressDialog';
import EntutoTextView from '../../components/common/EntutoTextView';
import ModuleHeaderText from '../../components/loginModule/ModuleHeaderText';
import LoginModuleProgress from '../../components/loginModule/LoginModuleProgress';
import LoginModuleTitle from '../../components/loginModule/LoginModuleTitle';
import EntutoEditText from '../../components/common/EntutoEditText';
import PrimaryButton from '../../components/common/PrimaryButton';
import ThemeColorComponent from '../../components/ThemeColorComponent';
import SearchIcon from '../../assets/Images/icon/search_icon.png';
import SelectBoxComponent from '../../components/common/SelectBoxComponent';
import {CommonActions} from '@react-navigation/native';
import ServerConnector from '../../utils/ServerConnector';
import SuccessFailureMsgBox from '../../components/common/SuccessFailureMsgBox';
import OptionSelectionItem from '../../components/common/OptionSelectionItem';
import StyleSelector from '../../components/Login/ThemeToggle';
import ActiveAngryIcon from '../../assets/Images/icon/reactions/Angry_active.png';
import InactiveAngryIcon from '../../assets/Images/icon/reactions/Angry_inactive.png';
import angryLottie from '../../assets/jsonfile/angry_lottie.json';

const QuickSignUpPersonalizeScreen = ({navigation}) => {
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const {defaultStyle} = useDefaultStyle();
  const [selectedStyle, setSelectedStyle] = useState(
    theme.appThemeType === 'LIGHT' ? 'Light' : 'Dark',
  );
  const [showLoading, setShowLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [errorMsgType, setErrorMsgType] = useState('FAILED');
  const [refreshKey, setRefreshKey] = useState(Math.random());

  // Default topics and genres that will be shown if API requests fail
  const defaultTopics = [
    {label: 'Technology', value: 'Technology'},
    {label: 'Health & Fitness', value: 'Health & Fitness'},
    {label: 'Business', value: 'Business'},
    {label: 'Arts & Culture', value: 'Arts & Culture'},
    {label: 'Travel', value: 'Travel'},
    {label: 'Food & Cooking', value: 'Food & Cooking'},
    {label: 'Science', value: 'Science'},
  ];

  const defaultGenres = [
    {label: 'Action', value: 'Action'},
    {label: 'Comedy', value: 'Comedy'},
    {label: 'Drama', value: 'Drama'},
    {label: 'Thriller', value: 'Thriller'},
    {label: 'Romance', value: 'Romance'},
    {label: 'Documentary', value: 'Documentary'},
    {label: 'Sci-Fi', value: 'Sci-Fi'},
  ];

  // Topic state variables
  const [topicList, setTopicList] = useState(defaultTopics);
  const [selectedInterests, setSelectedInterests] = useState([]);
  const [displayTopicList, setDisplayTopicList] = useState(defaultTopics);

  // Genre state variables
  const [genreList, setGenreList] = useState(defaultGenres);
  const [selectedGenres, setSelectedGenres] = useState([]);
  const [displayGenreList, setDisplayGenreList] = useState([]);
  const [backupGenreList, setBackupGenreList] = useState([]);
  //Emotions
  const [emotionList, setEmotionList] = useState([]);
  const [selectedEmotions, setSelectedEmotions] = useState([]);
  const [displayEmotionList, setDisplayEmotionList] = useState([]);

  const [selectedUIColor, setSelectedUIColor] = useState('COLOR_1');

  // Modal state variables
  const [openInterestList, setOpenInterestList] = useState(false);
  const [openGenreList, setOpenGenreList] = useState(false);
  const [openEmotionList, setOpenEmotionList] = useState(false);

  useEffect(() => {
    setShowLoading(true);
    getInterestListService();
    getGenreListService();
    setEmotionList(hardcodedEmotions);
    setDisplayEmotionList(hardcodedEmotions);
  }, []);

  const getInterestListService = () => {
    let hashMap = {
      _action_code: '11:GET_INTEREST_CATEGORIES', //genres
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        let tempList = [];
        data.data.map(item => {
          tempList.push({
            label: item.category,
            value: item.category,
          });
        });
        setTopicList(tempList);
        getUserInterestListService(tempList, data.random);
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        createUpdatedList(
          [],
          defaultTopics,
          defaultTopics.map(item => item.value),
        );
      },
    );
  };

  const getGenreListService = () => {
    let hashMap = {
      _action_code: '11:GET_GENRE_CATEGORIES', //genres
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        let tempList = [];
        data.data.map(item => {
          tempList.push({
            label: item.category,
            value: item.category,
          });
        });
        setGenreList(tempList);
        // Modified to directly use createUpdatedGenreList instead of calling getUserGenreListService
        createUpdatedGenreList(
          [],
          tempList,
          tempList.map(item => item.value),
        );
      },
      (errorCode, errorMessage, data) => {
        // failure method
        createUpdatedGenreList(
          [],
          defaultGenres,
          defaultGenres.map(item => item.value),
        );
      },
    );
  };

  const getUserInterestListService = (tempList, randomList) => {
    let hashMap = {
      _action_code: '11:GET_USER_INTERESTS',
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        let selectedInterestList = [];
        data.data.map(item => {
          selectedInterestList.push(item.interest_name);
        });
        setSelectedInterests(selectedInterestList);
        createUpdatedList(selectedInterestList, tempList, randomList);
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        createUpdatedList([], tempList, randomList);
      },
    );
  };

  // Removed getUserGenreListService function

  const letsGetStartedBtnClick = () => {
    if (formValid()) {
      setShowLoading(true);
      updateUserInterestService();
    }
  };
  const searchEmotionBtnPress = () => {
    setOpenEmotionList(true);
  };

  const formValid = () => {
    let isValid = true;
    console.log(selectedInterests.length, selectedGenres.length);
    if (selectedInterests.length < 6) {
      setErrorMsg(
        'Please select minimum of 6 interesting topics, for the best experience.',
      );
      setRefreshKey(Math.random());
      isValid = false;
    } else if (selectedGenres.length < 6) {
      setErrorMsg(
        'Please select minimum of 6 genres, for the best experience.',
      );
      setRefreshKey(Math.random());
      isValid = false;
    } else if (selectedEmotions.length < 6) {
      setErrorMsg(
        'Please select minimum of 6 emotions, for the best experience.',
      );
      setRefreshKey(Math.random());
      isValid = false;
    }
    return isValid;
  };

  const themeColorChange = themeValue => {
    setSelectedUIColor(themeValue);
  };

  const handleStyleChange = style => {
    setSelectedStyle(style);
    // Convert the style value to the format expected by the theme system
    const themeType = style === 'Light' ? 'LIGHT' : 'DARK';
    // Update the global theme state
    theme.changeAppTheme(themeType);
    // Persist the theme change in AsyncStorage
    _setAppThemeType(themeType);
  };

  const selectInterestBoxClick = (clickID, obj) => {
    setOpenInterestList(false);
    if (clickID == 'DONE') {
      setSelectedInterests([...[], ...obj.selectedItem]);
      createUpdatedList(
        obj.selectedItem,
        topicList,
        topicList.map(item => item.value),
      );
    }
  };

  const selectGenreBoxClick = (clickID, obj) => {
    setOpenGenreList(false);
    if (clickID == 'DONE') {
      setSelectedGenres([...[], ...obj.selectedItem]);
      createUpdatedGenreList(
        obj.selectedItem,
        genreList,
        genreList.map(item => item.value),
      );
    }
  };

  const createUpdatedList = (selectedItems, tempList, tempBackUplList) => {
    let tempTopicList = JSON.parse(JSON.stringify(tempList));
    let list = [];

    // First add all selected items
    tempTopicList.forEach((item, index) => {
      if (selectedItems.includes(item.value)) {
        item.isChecked = true;
        list.push(item);
      }
    });

    // Then add non-selected items until we reach max 6
    if (list.length < 6) {
      tempTopicList.forEach(item => {
        if (
          !selectedItems.includes(item.value) &&
          // tempBackUplList.includes(item.value) &&
          list.length < 6
        ) {
          item.isChecked = false;
          list.push(item);
        }
      });
    }

    setDisplayTopicList(list);
  };

  const createUpdatedGenreList = (selectedItems, tempList, tempBackUpList) => {
    let tempGenreList = JSON.parse(JSON.stringify(tempList));
    let list = [];

    // First add all selected items
    tempGenreList.forEach((item, index) => {
      if (selectedItems.includes(item.value)) {
        item.isChecked = true;
        list.push(item);
      }
    });

    // Then add non-selected items until we reach max 6
    if (list.length < 6) {
      tempGenreList.forEach(item => {
        if (
          !selectedItems.includes(item.value) &&
          // tempBackUpList.includes(item.value) &&
          list.length < 6
        ) {
          item.isChecked = false;
          list.push(item);
        }
      });
    }

    setDisplayGenreList(list);
  };

  const onInterestItemPress = (clickID, obj) => {
    if (clickID == 'ITEM_CLICK') {
      let tempTopicList = JSON.parse(JSON.stringify(displayTopicList));
      let list = [...tempTopicList];
      list[obj.index].isChecked = !list[obj.index].isChecked;
      if (list[obj.index].isChecked) {
        setSelectedInterests([...selectedInterests, obj.value]);
      } else {
        setSelectedInterests(
          selectedInterests.filter(item => item !== obj.value),
        );
      }
      setDisplayTopicList(list);
    }
  };

  const onGenreItemPress = (clickID, obj) => {
    if (clickID == 'ITEM_CLICK') {
      let tempGenreList = JSON.parse(JSON.stringify(displayGenreList));
      let list = [...tempGenreList];
      list[obj.index].isChecked = !list[obj.index].isChecked;
      if (list[obj.index].isChecked) {
        setSelectedGenres([...selectedGenres, obj.value]);
      } else {
        setSelectedGenres(selectedGenres.filter(item => item !== obj.value));
      }
      setDisplayGenreList(list);
    }
  };
  const onEmotionItemPress = (clickID, obj) => {
    if (clickID === 'ITEM_CLICK') {
      let tempEmotionList = JSON.parse(JSON.stringify(displayEmotionList));
      let list = [...tempEmotionList];
      list[obj.index].isChecked = !list[obj.index].isChecked;

      if (list[obj.index].isChecked) {
        setSelectedEmotions([...selectedEmotions, obj.value]);
      } else {
        setSelectedEmotions(
          selectedEmotions.filter(item => item !== obj.value),
        );
      }

      setDisplayEmotionList(list);
    }
  };
  const updateUserInterestService = () => {
    let hashMap = {
      _action_code: '11:UPDATE_USER_INTERESTS',
      interests: JSON.stringify(selectedInterests),
      genres: JSON.stringify(selectedGenres),
    };
    let connector = new ServerConnector();
    // connector.postData(
    //   hashMap,
    //   data => {
    //     // success method
    //     setShowLoading(true);
    //     updateUserColorService();
    //   },
    //   (errorCode, errorMessage, data) => {
    //     // failure method
    //     setShowLoading(false);
    //     var fieldErrorShown = false;
    //     if (errorCode === 'E006') {
    //       if (data && data != null && data.data) {
    //         if (data.data.interests) {
    //           setErrorMsg(data.data.interests);
    //           setErrorMsgType('FAILED');
    //           setRefreshKey(Math.random());
    //           fieldErrorShown = true;
    //         } else if (data.data.genres) {
    //           setErrorMsg(data.data.genres);
    //           setErrorMsgType('FAILED');
    //           setRefreshKey(Math.random());
    //           fieldErrorShown = true;
    //         }
    //       }
    //     }
    //     if (!fieldErrorShown) {
    //       setErrorMsg(errorMessage);
    //       setErrorMsgType('FAILED');
    //       setRefreshKey(Math.random());
    //     }
    //   },
    // );
    navigation.navigate('NotificationGuideSelectionScreen');
  };
  const selectEmotionBoxClick = (clickID, obj) => {
    setOpenEmotionList(false);
    if (clickID == 'DONE') {
      setSelectedEmotions([...[], ...obj.selectedItem]);
      // Create updated list of emotions
      let tempEmotionList = JSON.parse(JSON.stringify(displayEmotionList));
      tempEmotionList.forEach(item => {
        item.isChecked = obj.selectedItem.includes(item.value);
      });
      setDisplayEmotionList(tempEmotionList);
    }
  };
  const updateUserColorService = () => {
    let hashMap = {
      _action_code: '11:UPDATE_USER_UI_COLOUR',
      ui_colour: selectedUIColor,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        theme.changeThemeColor(selectedUIColor);
        navigation.dispatch(
          CommonActions.reset({
            index: 1,
            routes: [
              {
                name: 'VideoContentScreen',
                params: {
                  postSeq: -1,
                  postProfileSeq: -1,
                  cameFrom: 'PERSONALIZATION',
                },
              },
            ],
          }),
        );
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        var fieldErrorShown = false;
        if (errorCode === 'E006') {
          if (data && data != null && data.data) {
            if (data.data.ui_colour) {
              setErrorMsg(data.data.ui_colour);
              setErrorMsgType('FAILED');
              setRefreshKey(Math.random());
              fieldErrorShown = true;
            }
          }
        }
        if (!fieldErrorShown) {
          setErrorMsg(errorMessage);
          setErrorMsgType('FAILED');
          setRefreshKey(Math.random());
        }
      },
    );
  };

  const searchTopicBtnPress = () => {
    setOpenInterestList(true);
  };

  const searchGenreBtnPress = () => {
    setOpenGenreList(true);
  };

  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <CustomProgressDialog showLoading={showLoading} />
      <View style={{flex: 1, position: 'relative'}}>
        <LoginSignUpLinearGrad />
        <ModuleAppBar navigation={navigation} />
        <ScrollView keyboardShouldPersistTaps="handled">
          <View style={defaultStyle.loginModuleContainer}>
            <LoginModuleTitle
              firstTitleText={`Let's Personalize Your`}
              secondTitleText="Digital Experience!"
              style={{marginTop: theme.dimensions.loginModuleTitleMT}}
            />
            <View style={defaultStyle.loginModuleFormContainer}>
              <StyleSelector
                selectedStyle={selectedStyle}
                onStyleChange={handleStyleChange}
              />
              {/* Genre */}
              <View>
                <EntutoTextView style={style.labelText}>
                  What genres of content are you interested in?
                </EntutoTextView>
              </View>
              <EntutoTextView style={style.warringInterestText}>
                Select at least 9
              </EntutoTextView>

              <View
                style={{
                  marginTop: theme.dimensions.loginModuleInputMT,
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}>
                {displayGenreList.map((obj, i) => {
                  return (
                    <OptionSelectionItem
                      key={i}
                      index={i}
                      label={obj.label}
                      isChecked={obj.isChecked}
                      value={obj.value}
                      onItemSelected={onGenreItemPress}
                    />
                  );
                })}

                <OptionSelectionItem
                  isSearchIcon={true}
                  inactiveImageValue={SearchIcon}
                  activeImageValue={SearchIcon}
                  onItemSelected={searchGenreBtnPress}
                  showImage={true}
                />
              </View>

              {/* Topics */}
              <View
                style={{marginTop: theme.dimensions.loginModuleInputMT * 1.5}}>
                <EntutoTextView style={style.labelText}>
                  Why don't you select some topics as well?
                </EntutoTextView>
              </View>

              <EntutoTextView style={style.warringInterestText}>
                Select at least 9
              </EntutoTextView>

              <View
                style={{
                  marginTop: theme.dimensions.loginModuleInputMT,
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}>
                {displayTopicList.map((obj, i) => {
                  return (
                    <OptionSelectionItem
                      key={i}
                      index={i}
                      label={obj.label}
                      isChecked={obj.isChecked}
                      value={obj.value}
                      onItemSelected={onInterestItemPress}
                    />
                  );
                })}

                <OptionSelectionItem
                  isSearchIcon={true}
                  inactiveImageValue={SearchIcon}
                  activeImageValue={SearchIcon}
                  onItemSelected={searchTopicBtnPress}
                  showImage={true}
                />
              </View>
              {/* Emotions  */}
              <View
                style={{marginTop: theme.dimensions.loginModuleInputMT * 1.5}}>
                <EntutoTextView style={style.labelText}>
                  How about some emotions you connect with?
                </EntutoTextView>
              </View>

              <EntutoTextView style={style.warringInterestText}>
                Select at least 9
              </EntutoTextView>

              <View
                style={{
                  marginTop: theme.dimensions.loginModuleInputMT,
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}>
                {displayEmotionList.map((obj, i) => {
                  return (
                    <OptionSelectionItem
                      key={i}
                      index={i}
                      // label={obj.value}

                      isChecked={obj.isChecked}
                      value={obj.value}
                      onItemSelected={onEmotionItemPress}
                      inactiveImageValue={obj.inactivePath}
                      activeImageValue={obj.activePath}
                      showImage={true}
                      lottieFile={obj.lottiePath}
                      isAnimated={true}
                    />
                  );
                })}
                <OptionSelectionItem
                  isSearchIcon={true}
                  inactiveImageValue={SearchIcon}
                  activeImageValue={SearchIcon}
                  onItemSelected={searchEmotionBtnPress}
                  showImage={true}
                />
              </View>
              <View style={{marginTop: 54}}>
                <PrimaryButton
                  label="You Are All Set!"
                  style={{}}
                  uppercase={false}
                  onPress={() => letsGetStartedBtnClick()}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
      {/* Interest Selection Modal */}
      <Modal
        animationType="fade"
        visible={openInterestList}
        style={{margin: 0, flex: 1}}>
        <SelectBoxComponent
          selectBoxClick={selectInterestBoxClick}
          list={JSON.parse(JSON.stringify(topicList))}
          selectedValue={selectedInterests}
          title="Select Interest"
          maxSelectedValue={10}
          multiSelect={true}
          labelField="label"
          valueField="value"
        />
      </Modal>
      {/* Emotion Selection Modal */}
      <Modal
        animationType="fade"
        visible={openEmotionList}
        style={{margin: 0, flex: 1}}>
        <SelectBoxComponent
          selectBoxClick={selectEmotionBoxClick}
          list={JSON.parse(JSON.stringify(displayEmotionList))}
          selectedValue={selectedEmotions}
          title="Select Emotions"
          maxSelectedValue={6}
          multiSelect={true}
          isEmotions={true} // Set this to true for emotions
        />
      </Modal>
      {/* Genre Selection Modal */}
      <Modal
        animationType="fade"
        visible={openGenreList}
        style={{margin: 0, flex: 1}}>
        <SelectBoxComponent
          selectBoxClick={selectGenreBoxClick}
          list={JSON.parse(JSON.stringify(genreList))}
          selectedValue={selectedGenres}
          title="Select Genre"
          maxSelectedValue={7}
          multiSelect={true}
          labelField="label"
          valueField="value"
        />
      </Modal>

      {errorMsg.length != 0 ? (
        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
      ) : null}
    </>
  );
};

export default QuickSignUpPersonalizeScreen;

const styles = theme =>
  StyleSheet.create({
    labelText: {
      fontSize: theme.calculateFontSize(theme.dimensions.personalizeLabelText),
    },
    itemContainer: {
      padding: theme.dimensions.loginModuleInputPadding,
      borderWidth: 1,
      justifyContent: 'center',
      alignItems: 'center',
      height: 36,
      // borderRadius: 36 / 2,
      marginEnd: 8,
      marginBottom: 8,
      minWidth: 90,
    },
    itemContainerText: {
      paddingHorizontal: 25,
    },
    searchIcon: {
      width: 17,
      height: 17,
      resizeMode: 'contain',
      tintColor: '#707070',
      paddingHorizontal: 25,
    },
    warringInterestText: {
      color: '#FF0000',
      marginTop: 10,
    },
  });
const hardcodedEmotions = [
  {
    activePath: ActiveAngryIcon,
    inactivePath: InactiveAngryIcon,
    lottiePath: angryLottie,

    value: 'Angry',
    isChecked: false,
  },
  {
    activePath: ActiveAngryIcon,
    inactivePath: InactiveAngryIcon,
    lottiePath: angryLottie,
    value: 'Happy',
    isChecked: false,
  },
  {
    activePath: ActiveAngryIcon,
    inactivePath: InactiveAngryIcon,
    lottiePath: angryLottie,
    value: 'Sad',
    isChecked: false,
  },
  {
    activePath: ActiveAngryIcon,
    inactivePath: InactiveAngryIcon,
    lottiePath: angryLottie,
    value: 'Excited',
    isChecked: false,
  },
  {
    activePath: ActiveAngryIcon,
    inactivePath: InactiveAngryIcon,
    lottiePath: angryLottie,
    value: 'Surprised',
    isChecked: false,
  },
  {
    activePath: ActiveAngryIcon,
    inactivePath: InactiveAngryIcon,
    lottiePath: angryLottie,
    value: 'Calm',
    isChecked: false,
  },
];
